const express = require('express');
const router = express.Router();
const path = require('path');
const fs = require('fs');
const Feedback = require('../models/Feedback');
const { connectToDatabase, getConnectionStatus } = require('../utils/database');

// Home page route
router.get('/', (req, res) => {
  res.render('index', {
    title: 'Download Project',
    message: 'Welcome to the University of Layyah Portal'
  });
});

// Feedback page route
router.get('/feedback', async (req, res) => {
  try {
    let feedbacks = [];

    // Ensure database connection for serverless
    try {
      await connectToDatabase();
      console.log('Database connection status:', getConnectionStatus());

      const mongoFeedbacks = await Feedback.find({})
        .sort({ date: -1 })
        .limit(10)
        .select('rating content feedback_type liked_aspects improve_aspects date -_id');

      console.log('MongoDB feedbacks found:', mongoFeedbacks.length);

      if (mongoFeedbacks && mongoFeedbacks.length > 0) {
        feedbacks = mongoFeedbacks.map(feedback => ({
          rating: feedback.rating,
          content: feedback.content || 'No content provided',
          feedback_type: feedback.feedback_type || 'general',
          liked_aspects: feedback.liked_aspects || [],
          improve_aspects: feedback.improve_aspects || [],
          date: feedback.date || new Date()
        }));
        console.log('Using MongoDB data, feedbacks:', feedbacks.length);
      } else {
        console.log('No MongoDB data found');
        feedbacks = [];
      }
    } catch (dbError) {
      console.error('MongoDB error:', dbError.message);
      console.error('Full error:', dbError);
      console.log('No fallback files available - showing empty state');
      feedbacks = [];

      // Don't throw error, just continue with empty feedbacks
    }

    console.log('Final feedbacks to display:', feedbacks);

    res.render('feedback', {
      title: 'Submit Feedback',
      message: 'We value your feedback',
      feedbacks: feedbacks
    });
  } catch (error) {
    console.error('Error loading feedback:', error);
    res.render('feedback', {
      title: 'Submit Feedback',
      message: 'We value your feedback',
      feedbacks: []
    });
  }
});

// Process feedback submission
router.post('/feedback', async (req, res) => {
  console.log('=== FEEDBACK SUBMISSION START ===');
  try {
    // Ensure database connection for serverless
    await connectToDatabase();
    console.log('Database connection status for POST:', getConnectionStatus());

    // Log the received form data for debugging
    console.log('Feedback received:', req.body);
    console.log('Request headers:', req.headers['content-type']);

    // Validate required fields
    if (!req.body.name || !req.body.email || !req.body.rating || !req.body.comments || !req.body.feedback_type) {
      console.log('Validation failed - missing required fields');

      // Get existing feedbacks for display
      let feedbacks = [];
      try {
        const mongoFeedbacks = await Feedback.find({})
          .sort({ date: -1 })
          .limit(10)
          .select('rating content feedback_type liked_aspects improve_aspects date -_id');

        if (mongoFeedbacks && mongoFeedbacks.length > 0) {
          feedbacks = mongoFeedbacks.map(feedback => ({
            rating: feedback.rating,
            content: feedback.content || 'No content provided',
            feedback_type: feedback.feedback_type || 'general',
            liked_aspects: feedback.liked_aspects || [],
            improve_aspects: feedback.improve_aspects || [],
            date: feedback.date || new Date()
          }));
        }
      } catch (dbError) {
        console.error('Error loading feedbacks for validation error:', dbError.message);
      }

      return res.render('feedback', {
        title: 'Submit Feedback',
        message: 'Please fill in all required fields.',
        error: true,
        feedbacks: feedbacks
      });
    }

    const feedbackData = {
      name: req.body.name.trim(),
      email: req.body.email.trim().toLowerCase(),
      rating: parseInt(req.body.rating),
      occupation: req.body.occupation || 'Not specified',
      feedback_type: req.body.feedback_type,
      title: `${req.body.feedback_type.charAt(0).toUpperCase() + req.body.feedback_type.slice(1)} Feedback`,
      content: req.body.comments.trim(),
      liked_aspects: Array.isArray(req.body.liked_aspects) ? req.body.liked_aspects : (req.body.liked_aspects ? [req.body.liked_aspects] : []),
      improve_aspects: Array.isArray(req.body.improve_aspects) ? req.body.improve_aspects : (req.body.improve_aspects ? [req.body.improve_aspects] : []),
      date: new Date()
    };

    // Try to save to MongoDB
    let saveSuccess = false;
    let saveError = null;

    try {
      const feedback = new Feedback(feedbackData);
      const savedFeedback = await feedback.save();
      console.log('Feedback saved to MongoDB successfully:', savedFeedback._id);
      saveSuccess = true;
    } catch (dbError) {
      console.error('Error saving to MongoDB:', dbError);
      console.error('Error details:', {
        name: dbError.name,
        message: dbError.message,
        code: dbError.code,
        stack: dbError.stack
      });
      saveError = dbError;
      saveSuccess = false;

      // Don't hide the error in production - let user know something went wrong
      if (process.env.NODE_ENV === 'production') {
        console.log('Production environment - showing error to user');
      }
    }

    // Get updated feedback list to display
    let feedbacks = [];
    try {
      const mongoFeedbacks = await Feedback.find({})
        .sort({ date: -1 })
        .limit(10)
        .select('rating content feedback_type liked_aspects improve_aspects date -_id');

      if (mongoFeedbacks && mongoFeedbacks.length > 0) {
        feedbacks = mongoFeedbacks.map(feedback => ({
          rating: feedback.rating,
          content: feedback.content || 'No content provided',
          feedback_type: feedback.feedback_type || 'general',
          liked_aspects: feedback.liked_aspects || [],
          improve_aspects: feedback.improve_aspects || [],
          date: feedback.date || new Date()
        }));
      }
    } catch (dbError) {
      console.log('Error loading feedbacks for display:', dbError.message);
      feedbacks = []; // Ensure feedbacks is always an array
    }

    // Show appropriate message based on save status
    if (saveSuccess) {
      res.render('feedback', {
        title: 'Submit Feedback',
        message: 'Thank you for your feedback! It has been submitted successfully.',
        success: true,
        feedbacks: feedbacks
      });
    } else {
      res.render('feedback', {
        title: 'Submit Feedback',
        message: `Sorry, there was an error submitting your feedback. Please try again. ${saveError ? 'Error: ' + saveError.message : ''}`,
        error: true,
        feedbacks: feedbacks
      });
    }
  } catch (error) {
    console.error('=== FEEDBACK SUBMISSION ERROR ===');
    console.error('Error processing feedback:', error);
    console.error('Error stack:', error.stack);
    console.error('Request body:', req.body);

    // Get existing feedbacks for display even on error
    let feedbacks = [];
    try {
      const mongoFeedbacks = await Feedback.find({})
        .sort({ date: -1 })
        .limit(10)
        .select('rating content feedback_type liked_aspects improve_aspects date -_id');

      if (mongoFeedbacks && mongoFeedbacks.length > 0) {
        feedbacks = mongoFeedbacks.map(feedback => ({
          rating: feedback.rating,
          content: feedback.content || 'No content provided',
          feedback_type: feedback.feedback_type || 'general',
          liked_aspects: feedback.liked_aspects || [],
          improve_aspects: feedback.improve_aspects || [],
          date: feedback.date || new Date()
        }));
      }
    } catch (dbError) {
      console.log('Error loading feedbacks for error case:', dbError.message);
    }

    res.render('feedback', {
      title: 'Submit Feedback',
      message: 'There was an error submitting your feedback. Please try again later.',
      error: true,
      feedbacks: feedbacks
    });
  }
});

// Download route - serve the zip file with streaming
router.get('/download/portal.zip', (req, res) => {
  const filePath = path.join(__dirname, '../public/downloads/portal.zip');

  console.log('Download request for portal.zip');
  console.log('File path:', filePath);
  console.log('File exists:', fs.existsSync(filePath));

  // Check if file exists
  if (fs.existsSync(filePath)) {
    try {
      // Get file stats
      const stat = fs.statSync(filePath);

      // Set proper headers for download
      res.setHeader('Content-Type', 'application/zip');
      res.setHeader('Content-Disposition', 'attachment; filename="university_portal_project.zip"');
      res.setHeader('Content-Length', stat.size);
      res.setHeader('Cache-Control', 'no-cache');

      // Create read stream and pipe to response
      const readStream = fs.createReadStream(filePath);

      readStream.on('error', (err) => {
        console.error('Stream error:', err);
        if (!res.headersSent) {
          res.status(500).json({ error: 'Error streaming file' });
        }
      });

      readStream.pipe(res);

    } catch (err) {
      console.error('Download error:', err);
      res.status(500).render('index', {
        title: 'Download Project',
        message: 'Error downloading the file. Please try again later.',
        error: true
      });
    }
  } else {
    // Try alternative paths
    const altPaths = [
      path.join(__dirname, '../portal.zip'),
      path.join(process.cwd(), 'public/downloads/portal.zip'),
      path.join(process.cwd(), 'portal.zip')
    ];

    console.log('Primary path failed, trying alternatives:');
    let foundFile = null;

    for (const altPath of altPaths) {
      console.log('Checking:', altPath, 'exists:', fs.existsSync(altPath));
      if (fs.existsSync(altPath)) {
        foundFile = altPath;
        break;
      }
    }

    if (foundFile) {
      console.log('Found file at:', foundFile);
      try {
        res.setHeader('Content-Type', 'application/zip');
        res.setHeader('Content-Disposition', 'attachment; filename="university_portal_project.zip"');
        const readStream = fs.createReadStream(foundFile);
        readStream.pipe(res);
        return;
      } catch (err) {
        console.error('Error serving alternative file:', err);
      }
    }

    console.log('No file found at any location');
    res.status(404).render('index', {
      title: 'Download Project',
      message: 'The requested file is not available for download.',
      error: true
    });
  }
});

// Test route for small file
router.get('/download/test.txt', (req, res) => {
  const filePath = path.join(__dirname, '../public/downloads/test.txt');
  if (fs.existsSync(filePath)) {
    res.download(filePath, 'test.txt');
  } else {
    res.status(404).send('Test file not found');
  }
});

// Video streaming routes with proper headers for inline playback
router.get('/stream/student.mp4', (req, res) => {
  const videoPath = path.join(__dirname, '../public/images/student.mp4');

  if (!fs.existsSync(videoPath)) {
    return res.status(404).send('Video not found');
  }

  const stat = fs.statSync(videoPath);
  const fileSize = stat.size;
  const range = req.headers.range;

  if (range) {
    const parts = range.replace(/bytes=/, "").split("-");
    const start = parseInt(parts[0], 10);
    const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;
    const chunksize = (end - start) + 1;

    const file = fs.createReadStream(videoPath, { start, end });
    const head = {
      'Content-Range': `bytes ${start}-${end}/${fileSize}`,
      'Accept-Ranges': 'bytes',
      'Content-Length': chunksize,
      'Content-Type': 'video/mp4',
      'Cache-Control': 'no-cache'
    };
    res.writeHead(206, head);
    file.pipe(res);
  } else {
    const head = {
      'Content-Length': fileSize,
      'Content-Type': 'video/mp4',
      'Accept-Ranges': 'bytes',
      'Cache-Control': 'no-cache'
    };
    res.writeHead(200, head);
    fs.createReadStream(videoPath).pipe(res);
  }
});

router.get('/stream/professor.mp4', (req, res) => {
  const videoPath = path.join(__dirname, '../public/images/professor.mp4');

  if (!fs.existsSync(videoPath)) {
    return res.status(404).send('Video not found');
  }

  const stat = fs.statSync(videoPath);
  const fileSize = stat.size;
  const range = req.headers.range;

  if (range) {
    const parts = range.replace(/bytes=/, "").split("-");
    const start = parseInt(parts[0], 10);
    const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;
    const chunksize = (end - start) + 1;

    const file = fs.createReadStream(videoPath, { start, end });
    const head = {
      'Content-Range': `bytes ${start}-${end}/${fileSize}`,
      'Accept-Ranges': 'bytes',
      'Content-Length': chunksize,
      'Content-Type': 'video/mp4',
      'Cache-Control': 'no-cache'
    };
    res.writeHead(206, head);
    file.pipe(res);
  } else {
    const head = {
      'Content-Length': fileSize,
      'Content-Type': 'video/mp4',
      'Accept-Ranges': 'bytes',
      'Cache-Control': 'no-cache'
    };
    res.writeHead(200, head);
    fs.createReadStream(videoPath).pipe(res);
  }
});

router.get('/stream/admin.mp4', (req, res) => {
  const videoPath = path.join(__dirname, '../public/images/admin.mp4');

  if (!fs.existsSync(videoPath)) {
    return res.status(404).send('Video not found');
  }

  const stat = fs.statSync(videoPath);
  const fileSize = stat.size;
  const range = req.headers.range;

  if (range) {
    const parts = range.replace(/bytes=/, "").split("-");
    const start = parseInt(parts[0], 10);
    const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;
    const chunksize = (end - start) + 1;

    const file = fs.createReadStream(videoPath, { start, end });
    const head = {
      'Content-Range': `bytes ${start}-${end}/${fileSize}`,
      'Accept-Ranges': 'bytes',
      'Content-Length': chunksize,
      'Content-Type': 'video/mp4',
      'Cache-Control': 'no-cache'
    };
    res.writeHead(206, head);
    file.pipe(res);
  } else {
    const head = {
      'Content-Length': fileSize,
      'Content-Type': 'video/mp4',
      'Accept-Ranges': 'bytes',
      'Cache-Control': 'no-cache'
    };
    res.writeHead(200, head);
    fs.createReadStream(videoPath).pipe(res);
  }
});

// Videos page route
router.get('/videos', (req, res) => {
  res.render('videos', {
    title: 'Project Videos',
    message: 'Watch our project demonstrations'
  });
});

// Redirect /download to home page
router.get('/download', (req, res) => {
  res.redirect('/');
});

module.exports = router;
