:root {
  --primary-color: #f89406; /* Orange from the logo */
  --primary-light: #ffa726; /* Lighter orange */
  --primary-dark: #e67e00; /* Darker orange */
  --secondary-color: #1a9d55; /* Green from the logo */
  --secondary-light: #2ebd6e; /* Lighter green */
  --secondary-dark: #0e7d3e; /* Darker green */
  --dark-color: #333333; /* Dark gray/black from the logo */
  --dark-light: #555555; /* Lighter dark */
  --light-color: #f8f9fa; /* Light background */
  --white-color: #ffffff;
  --danger-color: #dc3545;
  --success-color: #28a745;
  --info-color: #17a2b8;
  --warning-color: #ffc107;
  --gray-100: #f8f9fa;
  --gray-200: #e9ecef;
  --gray-300: #dee2e6;
  --gray-400: #ced4da;
  --gray-500: #adb5bd;
  --gray-600: #6c757d;
  --gray-700: #495057;
  --gray-800: #343a40;
  --gray-900: #212529;
}

/* Base Styles */
body {
  font-family: 'Poppins', 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  background-color: var(--light-color);
  color: var(--gray-800);
  overflow-x: hidden;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: 1rem;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: all 0.3s ease;
}

a:hover {
  color: var(--primary-dark);
  text-decoration: none;
}

/* Navbar Styles */
.navbar {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark)) !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 1rem 0;
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.5rem;
}

.navbar-brand img {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
  transition: transform 0.3s ease;
}

.navbar-brand:hover img {
  transform: scale(1.05);
}

.nav-link {
  font-weight: 500;
  padding: 0.5rem 1rem !important;
  margin: 0 0.25rem;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.nav-link:hover, .nav-link.active {
  background-color: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

/* Button Styles */
.btn {
  font-weight: 600;
  padding: 0.6rem 1.5rem;
  border-radius: 5px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn-lg {
  padding: 0.8rem 2rem;
  font-size: 1.1rem;
}

.btn-primary {
  background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
  border: none;
}

.btn-primary:hover {
  background: linear-gradient(to right, var(--primary-dark), var(--primary-color));
  transform: translateY(-3px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

.btn-secondary {
  background: linear-gradient(to right, var(--secondary-color), var(--secondary-dark));
  border: none;
}

.btn-secondary:hover {
  background: linear-gradient(to right, var(--secondary-dark), var(--secondary-color));
  transform: translateY(-3px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

/* Footer Styles */
.footer {
  background: linear-gradient(to right, var(--dark-color), var(--gray-800));
  color: var(--white-color);
  padding: 3rem 0 2rem;
  margin-top: 4rem;
  position: relative;
}

.footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
}

.footer h5 {
  color: var(--primary-light);
  font-weight: 700;
  margin-bottom: 1.5rem;
  position: relative;
  display: inline-block;
}

.footer h5::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 40px;
  height: 3px;
  background-color: var(--primary-color);
}

.footer a {
  color: var(--gray-300);
  transition: all 0.3s ease;
}

.footer a:hover {
  color: var(--primary-light);
  transform: translateX(5px);
}

.footer ul li {
  margin-bottom: 0.75rem;
}

/* Card Styles */
.card {
  border: none;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  overflow: hidden;
  height: 100%;
}

.card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.card-body {
  padding: 2rem;
}

.card-title {
  font-weight: 700;
  margin-bottom: 1.25rem;
  position: relative;
  padding-bottom: 0.75rem;
}

.card-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 3px;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
}

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: var(--white-color);
  padding: 5rem 0;
  margin-bottom: 4rem;
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.1);
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
  opacity: 0.3;
}

.hero-section img {
  filter: drop-shadow(0 5px 15px rgba(0, 0, 0, 0.2));
  animation: float 3s ease-in-out infinite;
  max-width: 100%;
  height: auto;
}

@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

/* Fix for animation performance */
.animate__animated {
  will-change: transform, opacity;
  backface-visibility: hidden;
  transform: translateZ(0);
}

/* Download Section */
.download-section {
  background-color: var(--white-color);
  padding: 3rem;
  border-radius: 15px;
  margin: 2rem 0;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05);
  position: relative;
  overflow: hidden;
}

.download-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
}

/* Modern Feedback Page Styles */
.feedback-container {
  font-family: var(--bs-font-sans-serif);
}

.feedback-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, #ff8a00 100%);
  padding: 4rem 0;
  margin-bottom: 2rem;
}

.feedback-header .rating-display {
  font-size: 2rem;
  color: #FFD700;
  margin-top: 1rem;
}

.feedback-form-card {
  border: none;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.form-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.section-title {
  position: relative;
  padding-bottom: 0.5rem;
  margin-bottom: 1.5rem;
  font-weight: 600;
  color: var(--gray-800);
}

.section-title::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 50px;
  height: 3px;
  background-color: var(--primary-color);
  border-radius: 3px;
}

.form-label {
  font-weight: 600;
  color: var(--gray-700);
  margin-bottom: 0.5rem;
}

.form-control, .form-select {
  border: 1px solid var(--gray-300);
  border-radius: 8px;
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(248, 148, 6, 0.15);
}

.form-control-lg, .form-select-lg {
  height: auto;
  padding: 0.75rem 1rem;
  font-size: 1rem;
}

.form-check-input:checked {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

/* Button group for feedback type */
.feedback-type-buttons {
  width: 100%;
  flex-wrap: wrap;
}

.feedback-type-buttons .btn {
  flex: 1;
  min-width: 100px;
  margin-bottom: 0.5rem;
  border-radius: 6px !important;
  margin-right: 0.5rem;
}

.btn-check:checked + .btn-outline-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

/* Aspect cards */
.aspect-card {
  background-color: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.aspect-card:hover {
  background-color: #f0f2f5;
}

.aspect-card .h6 {
  font-weight: 600;
  color: var(--gray-700);
}

/* Submit button */
.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background-color: #e07e00;
  border-color: #e07e00;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(248, 148, 6, 0.3);
}

/* Mobile-friendly improvements */
@media (max-width: 768px) {
  .feedback-header {
    padding: 3rem 0;
  }

  .feedback-form-card {
    border-radius: 12px;
  }

  .form-control, .form-select {
    font-size: 16px; /* Prevents iOS zoom on focus */
  }

  .feedback-type-buttons .btn {
    margin-bottom: 0.5rem;
    width: calc(50% - 0.5rem);
    flex: none;
  }

  /* Improve touch targets */
  .form-check-label {
    padding: 0.25rem 0;
    display: inline-block;
  }

  .btn-primary {
    width: 100%;
    padding: 0.75rem 1.5rem;
  }
}

@media (max-width: 576px) {
  .feedback-header {
    padding: 2rem 0;
  }

  .feedback-header h1 {
    font-size: 1.75rem;
  }

  .feedback-type-buttons .btn {
    width: 100%;
    margin-right: 0;
  }
}

/* Utility Classes */
.text-primary {
  color: var(--primary-color) !important;
}

.text-secondary {
  color: var(--secondary-color) !important;
}

.bg-primary {
  background-color: var(--primary-color) !important;
}

.bg-secondary {
  background-color: var(--secondary-color) !important;
}

.shadow-sm {
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05) !important;
}

.shadow {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05) !important;
}

.shadow-lg {
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.05) !important;
}

/* Video Styles */
.videos-header {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  padding: 4rem 0;
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
}

.videos-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  opacity: 0.3;
}

.video-stats .stat-item {
  text-align: center;
  padding: 0 1rem;
}

.video-card {
  transition: all 0.3s ease;
  border: none;
  overflow: hidden;
}

.video-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1) !important;
}

.video-wrapper {
  position: relative;
  overflow: hidden;
}

.video-overlay {
  background: rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  cursor: pointer;
}

.video-overlay:hover {
  background: rgba(0, 0, 0, 0.5);
}

.video-overlay .play-button {
  transition: all 0.3s ease;
}

.video-overlay:hover .play-button {
  transform: scale(1.1);
}

.video-preview-card {
  transition: all 0.3s ease;
  border: none;
}

.video-preview-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1) !important;
}

.video-preview-card .video-thumbnail {
  overflow: hidden;
}

.video-preview-card .play-overlay {
  transition: all 0.3s ease;
  cursor: pointer;
}

.video-preview-card:hover .play-overlay {
  background: rgba(0, 0, 0, 0.7) !important;
}

.video-preview-card .play-button {
  transition: all 0.3s ease;
}

.video-preview-card:hover .play-button {
  transform: scale(1.2);
}

.videos-preview-section {
  background-color: var(--gray-100);
  border-radius: 0;
  margin: 3rem 0;
}

.feature-item {
  font-size: 0.9rem;
  color: var(--gray-600);
}

.icon-wrapper {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* Video responsive styles */
video {
  border-radius: 8px 8px 0 0;
  transition: all 0.3s ease;
}

video:hover {
  transform: scale(1.02);
}

/* Call to action section in videos page */
.cta-section {
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
  opacity: 0.3;
}

/* Mobile video styles */
@media (max-width: 768px) {
  .videos-header {
    padding: 3rem 0;
  }

  .video-stats {
    flex-direction: column;
    gap: 1rem !important;
  }

  .video-stats .stat-item {
    padding: 0;
  }

  .video-card video {
    height: 200px !important;
  }

  .cta-section {
    padding: 3rem 1.5rem !important;
  }

  .cta-section .d-flex {
    flex-direction: column;
    gap: 1rem;
  }

  .cta-section .btn {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .videos-header h1 {
    font-size: 2rem;
  }

  .video-card {
    margin-bottom: 2rem;
  }

  .video-preview-card {
    margin-bottom: 1.5rem;
  }
}

/* Back to top button */
.back-to-top {
  position: fixed;
  right: 15px;
  bottom: 15px;
  z-index: 99;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: var(--primary-color);
  color: var(--white-color);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  transition: all 0.4s;
  opacity: 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.back-to-top:hover {
  background-color: var(--primary-dark);
  transform: translateY(-3px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Custom form styles for better stability */
.custom-checkbox .form-check-input:checked ~ .form-check-label {
  color: var(--primary-color);
  font-weight: 600;
}

.rating-stars-display i {
  margin: 0 2px;
}

/* Fix for input groups */
.input-group {
  position: relative;
  display: flex;
  flex-wrap: nowrap;
  align-items: stretch;
  width: 100%;
}

.input-group > .form-control {
  position: relative;
  flex: 1 1 auto;
  width: 1%;
  min-width: 0;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
  .hero-section {
    padding: 4rem 0;
  }

  .hero-section h1 {
    font-size: 2.5rem;
  }

  .card-body {
    padding: 1.5rem;
  }

  .download-section {
    padding: 2rem;
  }

  .feedback-form {
    padding: 1.5rem;
  }

  /* Improve form layout on tablets */
  .rating-select {
    flex-direction: column;
    align-items: flex-start;
  }

  .rating-stars-display {
    margin-left: 0 !important;
    margin-top: 0.5rem;
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding: 3rem 0;
  }

  .hero-section h1 {
    font-size: 2rem;
  }

  .navbar-brand span {
    font-size: 1.2rem;
  }

  .feature-icon {
    width: 50px !important;
    height: 50px !important;
  }

  .card-title {
    font-size: 1.25rem;
  }

  /* Improve form layout on mobile */
  .card-header h4 {
    font-size: 1.2rem;
  }

  .form-label {
    font-size: 0.9rem;
  }

  /* Make input groups stack better on mobile */
  .input-group {
    flex-wrap: wrap;
  }

  .input-group-text {
    border-radius: 4px 4px 0 0 !important;
    width: 100%;
    justify-content: center;
  }

  .input-group > .form-control {
    border-radius: 0 0 4px 4px !important;
  }
}

@media (max-width: 576px) {
  .hero-section {
    padding: 2rem 0;
  }

  .hero-section h1 {
    font-size: 1.75rem;
  }

  .hero-section .lead {
    font-size: 1rem !important;
  }

  /* Adjust rating stars size on mobile */
  .rating-stars {
    font-size: 1.5rem !important;
  }

  .rating-stars-display {
    font-size: 1.2rem;
  }

  .btn-lg {
    padding: 0.5rem 1.5rem;
    font-size: 1rem;
  }

  .download-section {
    padding: 1.5rem;
  }

  .card-body {
    padding: 1.25rem;
  }

  /* Improve form layout on small mobile */
  .card-header {
    padding: 0.75rem 1rem;
  }

  .card-header h4 {
    font-size: 1.1rem;
    margin-bottom: 0;
  }

  /* Make checkboxes more touch-friendly */
  .form-check {
    margin-bottom: 0.75rem;
  }

  .form-check-label {
    font-size: 0.9rem;
  }

  .form-check-input {
    width: 1.1em;
    height: 1.1em;
    margin-top: 0.25em;
  }

  /* Fix submit button on small screens */
  .text-center .btn-lg {
    width: 100%;
    max-width: 300px;
  }

  .footer {
    text-align: center;
  }

  .footer h5::after {
    left: 50%;
    transform: translateX(-50%);
  }
}
